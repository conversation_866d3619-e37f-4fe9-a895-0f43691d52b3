apiVersion: configuration.konghq.com/v1beta1
kind: KongUpstreamPolicy
metadata:
  name: sticky-sessions-example
  namespace: default
spec:
  # Enable sticky sessions for this upstream
  # This will route requests from the same client to the same backend target
  # based on a cookie. Requires Kong Enterprise Gateway.
  stickySessions:
    # Required: Name of the cookie to use for sticky sessions
    cookie: "session-id"
    # Optional: Path to set in the cookie (defaults to "/")
    cookiePath: "/"

---
apiVersion: configuration.konghq.com/v1beta1
kind: KongUpstreamPolicy
metadata:
  name: minimal-sticky-sessions
  namespace: default
spec:
  # Minimal sticky session configuration
  # cookiePath will default to "/"
  stickySessions:
    cookie: "my-session"

---
apiVersion: configuration.konghq.com/v1beta1
kind: KongUpstreamPolicy
metadata:
  name: explicit-hash-on-none
  namespace: default
spec:
  # Explicit hash_on: none configuration
  # This disables consistent hashing
  hashOn:
    input: "none"

---
apiVersion: configuration.konghq.com/v1beta1
kind: KongUpstreamPolicy
metadata:
  name: sticky-with-explicit-none
  namespace: default
spec:
  # Sticky sessions with explicit hash_on: none
  # Both configurations work together
  hashOn:
    input: "none"
  stickySessions:
    cookie: "session-id"
    cookiePath: "/"

---
# Example of applying the policy to a Service
apiVersion: v1
kind: Service
metadata:
  name: my-service
  namespace: default
  annotations:
    # Apply the sticky session policy to this service
    konghq.com/upstream-policy: sticky-sessions-example
spec:
  selector:
    app: my-app
  ports:
  - port: 80
    targetPort: 8080
