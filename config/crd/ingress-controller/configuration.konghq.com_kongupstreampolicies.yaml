---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: ingress-controller
    kubernetes-configuration.konghq.com/version: v1.4.0
  labels:
    gateway.networking.k8s.io/policy: direct
  name: kongupstreampolicies.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    - kong
    kind: KongUpstreamPolicy
    listKind: KongUpstreamPolicyList
    plural: kongupstreampolicies
    shortNames:
    - kup
    singular: kongupstreampolicy
  scope: Namespaced
  versions:
  - name: v1beta1
    schema:
      openAPIV3Schema:
        description: |-
          KongUpstreamPolicy allows configuring algorithm that should be used for load balancing traffic between Kong
          Upstream's Targets. It also allows configuring health checks for Kong Upstream's Targets.

          Its configuration is similar to Kong Upstream object (https://docs.konghq.com/gateway/latest/admin-api/#upstream-object),
          and it is applied to Kong Upstream objects created by the controller.

          It can be attached to Services. To attach it to a Service, it has to be annotated with
          `konghq.com/upstream-policy: <name>`, where `<name>` is the name of the KongUpstreamPolicy
          object in the same namespace as the Service.

          When attached to a Service, it will affect all Kong Upstreams created for the Service.

          When attached to a Service used in a Gateway API *Route rule with multiple BackendRefs, all of its Services MUST
          be configured with the same KongUpstreamPolicy. Otherwise, the controller will *ignore* the KongUpstreamPolicy.

          Note: KongUpstreamPolicy doesn't implement Gateway API's GEP-713 strictly.
          In particular, it doesn't use the TargetRef for attaching to Services and Gateway API *Routes - annotations are
          used instead. This is to allow reusing the same KongUpstreamPolicy for multiple Services and Gateway API *Routes.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: Spec contains the configuration of the Kong upstream.
            properties:
              algorithm:
                description: |-
                  Algorithm is the load balancing algorithm to use.
                  Accepted values are: "round-robin", "consistent-hashing", "least-connections", "latency".
                enum:
                - round-robin
                - consistent-hashing
                - least-connections
                - latency
                type: string
              hashOn:
                description: |-
                  HashOn defines how to calculate hash for consistent-hashing load balancing algorithm.
                  Algorithm must be set to "consistent-hashing" for this field to have effect.
                properties:
                  cookie:
                    description: Cookie is the name of the cookie to use as hash input.
                    type: string
                  cookiePath:
                    description: CookiePath is cookie path to set in the response
                      headers.
                    type: string
                  header:
                    description: Header is the name of the header to use as hash input.
                    type: string
                  input:
                    description: |-
                      Input allows using one of the predefined inputs (ip, consumer, path).
                      For other parametrized inputs, use one of the fields below.
                    enum:
                    - ip
                    - consumer
                    - path
                    - none
                    type: string
                  queryArg:
                    description: QueryArg is the name of the query argument to use
                      as hash input.
                    type: string
                  uriCapture:
                    description: URICapture is the name of the URI capture group to
                      use as hash input.
                    type: string
                type: object
              hashOnFallback:
                description: |-
                  HashOnFallback defines how to calculate hash for consistent-hashing load balancing algorithm if the primary hash
                  function fails.
                  Algorithm must be set to "consistent-hashing" for this field to have effect.
                properties:
                  cookie:
                    description: Cookie is the name of the cookie to use as hash input.
                    type: string
                  cookiePath:
                    description: CookiePath is cookie path to set in the response
                      headers.
                    type: string
                  header:
                    description: Header is the name of the header to use as hash input.
                    type: string
                  input:
                    description: |-
                      Input allows using one of the predefined inputs (ip, consumer, path).
                      For other parametrized inputs, use one of the fields below.
                    enum:
                    - ip
                    - consumer
                    - path
                    - none
                    type: string
                  queryArg:
                    description: QueryArg is the name of the query argument to use
                      as hash input.
                    type: string
                  uriCapture:
                    description: URICapture is the name of the URI capture group to
                      use as hash input.
                    type: string
                type: object
              healthchecks:
                description: Healthchecks defines the health check configurations
                  in Kong.
                properties:
                  active:
                    description: Active configures active health check probing.
                    properties:
                      concurrency:
                        description: Concurrency is the number of targets to check
                          concurrently.
                        minimum: 1
                        type: integer
                      headers:
                        additionalProperties:
                          items:
                            type: string
                          type: array
                        description: Headers is a list of HTTP headers to add to the
                          probe request.
                        type: object
                      healthy:
                        description: Healthy configures thresholds and HTTP status
                          codes to mark targets healthy for an upstream.
                        properties:
                          httpStatuses:
                            description: HTTPStatuses is a list of HTTP status codes
                              that Kong considers a success.
                            items:
                              description: HTTPStatus is an HTTP status code.
                              maximum: 599
                              minimum: 100
                              type: integer
                            type: array
                          interval:
                            description: Interval is the interval between active health
                              checks for an upstream in seconds when in a healthy
                              state.
                            minimum: 0
                            type: integer
                          successes:
                            description: Successes is the number of successes to consider
                              a target healthy.
                            minimum: 0
                            type: integer
                        type: object
                      httpPath:
                        description: HTTPPath is the path to use in GET HTTP request
                          to run as a probe.
                        pattern: ^/.*$
                        type: string
                      httpsSni:
                        description: HTTPSSNI is the SNI to use in GET HTTPS request
                          to run as a probe.
                        type: string
                      httpsVerifyCertificate:
                        description: HTTPSVerifyCertificate is a boolean value that
                          indicates if the certificate should be verified.
                        type: boolean
                      timeout:
                        description: Timeout is the probe timeout in seconds.
                        minimum: 0
                        type: integer
                      type:
                        description: |-
                          Type determines whether to perform active health checks using HTTP or HTTPS, or just attempt a TCP connection.
                          Accepted values are "http", "https", "tcp", "grpc", "grpcs".
                        enum:
                        - http
                        - https
                        - tcp
                        - grpc
                        - grpcs
                        type: string
                      unhealthy:
                        description: Unhealthy configures thresholds and HTTP status
                          codes to mark targets unhealthy for an upstream.
                        properties:
                          httpFailures:
                            description: HTTPFailures is the number of failures to
                              consider a target unhealthy.
                            minimum: 0
                            type: integer
                          httpStatuses:
                            description: HTTPStatuses is a list of HTTP status codes
                              that Kong considers a failure.
                            items:
                              description: HTTPStatus is an HTTP status code.
                              maximum: 599
                              minimum: 100
                              type: integer
                            type: array
                          interval:
                            description: Interval is the interval between active health
                              checks for an upstream in seconds when in an unhealthy
                              state.
                            minimum: 0
                            type: integer
                          tcpFailures:
                            description: TCPFailures is the number of TCP failures
                              in a row to consider a target unhealthy.
                            minimum: 0
                            type: integer
                          timeouts:
                            description: Timeouts is the number of timeouts in a row
                              to consider a target unhealthy.
                            minimum: 0
                            type: integer
                        type: object
                    type: object
                  passive:
                    description: Passive configures passive health check probing.
                    properties:
                      healthy:
                        description: Healthy configures thresholds and HTTP status
                          codes to mark targets healthy for an upstream.
                        properties:
                          httpStatuses:
                            description: HTTPStatuses is a list of HTTP status codes
                              that Kong considers a success.
                            items:
                              description: HTTPStatus is an HTTP status code.
                              maximum: 599
                              minimum: 100
                              type: integer
                            type: array
                          interval:
                            description: Interval is the interval between active health
                              checks for an upstream in seconds when in a healthy
                              state.
                            minimum: 0
                            type: integer
                          successes:
                            description: Successes is the number of successes to consider
                              a target healthy.
                            minimum: 0
                            type: integer
                        type: object
                      type:
                        description: |-
                          Type determines whether to perform passive health checks interpreting HTTP/HTTPS statuses,
                          or just check for TCP connection success.
                          Accepted values are "http", "https", "tcp", "grpc", "grpcs".
                        enum:
                        - http
                        - https
                        - tcp
                        - grpc
                        - grpcs
                        type: string
                      unhealthy:
                        description: Unhealthy configures thresholds and HTTP status
                          codes to mark targets unhealthy.
                        properties:
                          httpFailures:
                            description: HTTPFailures is the number of failures to
                              consider a target unhealthy.
                            minimum: 0
                            type: integer
                          httpStatuses:
                            description: HTTPStatuses is a list of HTTP status codes
                              that Kong considers a failure.
                            items:
                              description: HTTPStatus is an HTTP status code.
                              maximum: 599
                              minimum: 100
                              type: integer
                            type: array
                          interval:
                            description: Interval is the interval between active health
                              checks for an upstream in seconds when in an unhealthy
                              state.
                            minimum: 0
                            type: integer
                          tcpFailures:
                            description: TCPFailures is the number of TCP failures
                              in a row to consider a target unhealthy.
                            minimum: 0
                            type: integer
                          timeouts:
                            description: Timeouts is the number of timeouts in a row
                              to consider a target unhealthy.
                            minimum: 0
                            type: integer
                        type: object
                    type: object
                  threshold:
                    description: |-
                      Threshold is the minimum percentage of the upstream’s targets’ weight that must be available for the whole
                      upstream to be considered healthy.
                    type: integer
                type: object
              slots:
                description: |-
                  Slots is the number of slots in the load balancer algorithm.
                  If not set, the default value in Kong for the algorithm is used.
                maximum: 65536
                minimum: 10
                type: integer
              stickySessions:
                description: |-
                  StickySessions defines the sticky session configuration for the upstream.
                  When enabled, clients will be routed to the same backend target based on a cookie.
                  This requires Kong Enterprise Gateway and sets hash_on to "none".
                properties:
                  cookie:
                    description: |-
                      Cookie is the name of the cookie to use for sticky sessions.
                      Kong will generate this cookie if it doesn't exist in the request.
                    minLength: 1
                    type: string
                  cookiePath:
                    default: /
                    description: |-
                      CookiePath is the path to set in the cookie.
                      If not specified, defaults to "/".
                    type: string
                required:
                - cookie
                type: object
            type: object
          status:
            description: Status defines the current state of KongUpstreamPolicy
            properties:
              ancestors:
                description: |-
                  Ancestors is a list of ancestor resources (usually Gateways) that are
                  associated with the policy, and the status of the policy with respect to
                  each ancestor. When this policy attaches to a parent, the controller that
                  manages the parent and the ancestors MUST add an entry to this list when
                  the controller first sees the policy and SHOULD update the entry as
                  appropriate when the relevant ancestor is modified.

                  Note that choosing the relevant ancestor is left to the Policy designers;
                  an important part of Policy design is designing the right object level at
                  which to namespace this status.

                  Note also that implementations MUST ONLY populate ancestor status for
                  the Ancestor resources they are responsible for. Implementations MUST
                  use the ControllerName field to uniquely identify the entries in this list
                  that they are responsible for.

                  Note that to achieve this, the list of PolicyAncestorStatus structs
                  MUST be treated as a map with a composite key, made up of the AncestorRef
                  and ControllerName fields combined.

                  A maximum of 16 ancestors will be represented in this list. An empty list
                  means the Policy is not relevant for any ancestors.

                  If this slice is full, implementations MUST NOT add further entries.
                  Instead they MUST consider the policy unimplementable and signal that
                  on any related resources such as the ancestor that would be referenced
                  here. For example, if this list was full on BackendTLSPolicy, no
                  additional Gateways would be able to reference the Service targeted by
                  the BackendTLSPolicy.
                items:
                  description: |-
                    PolicyAncestorStatus describes the status of a route with respect to an
                    associated Ancestor.

                    Ancestors refer to objects that are either the Target of a policy or above it
                    in terms of object hierarchy. For example, if a policy targets a Service, the
                    Policy's Ancestors are, in order, the Service, the HTTPRoute, the Gateway, and
                    the GatewayClass. Almost always, in this hierarchy, the Gateway will be the most
                    useful object to place Policy status on, so we recommend that implementations
                    SHOULD use Gateway as the PolicyAncestorStatus object unless the designers
                    have a _very_ good reason otherwise.

                    In the context of policy attachment, the Ancestor is used to distinguish which
                    resource results in a distinct application of this policy. For example, if a policy
                    targets a Service, it may have a distinct result per attached Gateway.

                    Policies targeting the same resource may have different effects depending on the
                    ancestors of those resources. For example, different Gateways targeting the same
                    Service may have different capabilities, especially if they have different underlying
                    implementations.

                    For example, in BackendTLSPolicy, the Policy attaches to a Service that is
                    used as a backend in a HTTPRoute that is itself attached to a Gateway.
                    In this case, the relevant object for status is the Gateway, and that is the
                    ancestor object referred to in this status.

                    Note that a parent is also an ancestor, so for objects where the parent is the
                    relevant object for status, this struct SHOULD still be used.

                    This struct is intended to be used in a slice that's effectively a map,
                    with a composite key made up of the AncestorRef and the ControllerName.
                  properties:
                    ancestorRef:
                      description: |-
                        AncestorRef corresponds with a ParentRef in the spec that this
                        PolicyAncestorStatus struct describes the status of.
                      properties:
                        group:
                          default: gateway.networking.k8s.io
                          description: |-
                            Group is the group of the referent.
                            When unspecified, "gateway.networking.k8s.io" is inferred.
                            To set the core API group (such as for a "Service" kind referent),
                            Group must be explicitly set to "" (empty string).

                            Support: Core
                          maxLength: 253
                          pattern: ^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                          type: string
                        kind:
                          default: Gateway
                          description: |-
                            Kind is kind of the referent.

                            There are two kinds of parent resources with "Core" support:

                            * Gateway (Gateway conformance profile)
                            * Service (Mesh conformance profile, ClusterIP Services only)

                            Support for other resources is Implementation-Specific.
                          maxLength: 63
                          minLength: 1
                          pattern: ^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$
                          type: string
                        name:
                          description: |-
                            Name is the name of the referent.

                            Support: Core
                          maxLength: 253
                          minLength: 1
                          type: string
                        namespace:
                          description: |-
                            Namespace is the namespace of the referent. When unspecified, this refers
                            to the local namespace of the Route.

                            Note that there are specific rules for ParentRefs which cross namespace
                            boundaries. Cross-namespace references are only valid if they are explicitly
                            allowed by something in the namespace they are referring to. For example:
                            Gateway has the AllowedRoutes field, and ReferenceGrant provides a
                            generic way to enable any other kind of cross-namespace reference.

                            <gateway:experimental:description>
                            ParentRefs from a Route to a Service in the same namespace are "producer"
                            routes, which apply default routing rules to inbound connections from
                            any namespace to the Service.

                            ParentRefs from a Route to a Service in a different namespace are
                            "consumer" routes, and these routing rules are only applied to outbound
                            connections originating from the same namespace as the Route, for which
                            the intended destination of the connections are a Service targeted as a
                            ParentRef of the Route.
                            </gateway:experimental:description>

                            Support: Core
                          maxLength: 63
                          minLength: 1
                          pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                          type: string
                        port:
                          description: |-
                            Port is the network port this Route targets. It can be interpreted
                            differently based on the type of parent resource.

                            When the parent resource is a Gateway, this targets all listeners
                            listening on the specified port that also support this kind of Route(and
                            select this Route). It's not recommended to set `Port` unless the
                            networking behaviors specified in a Route must apply to a specific port
                            as opposed to a listener(s) whose port(s) may be changed. When both Port
                            and SectionName are specified, the name and port of the selected listener
                            must match both specified values.

                            <gateway:experimental:description>
                            When the parent resource is a Service, this targets a specific port in the
                            Service spec. When both Port (experimental) and SectionName are specified,
                            the name and port of the selected port must match both specified values.
                            </gateway:experimental:description>

                            Implementations MAY choose to support other parent resources.
                            Implementations supporting other types of parent resources MUST clearly
                            document how/if Port is interpreted.

                            For the purpose of status, an attachment is considered successful as
                            long as the parent resource accepts it partially. For example, Gateway
                            listeners can restrict which Routes can attach to them by Route kind,
                            namespace, or hostname. If 1 of 2 Gateway listeners accept attachment
                            from the referencing Route, the Route MUST be considered successfully
                            attached. If no Gateway listeners accept attachment from this Route,
                            the Route MUST be considered detached from the Gateway.

                            Support: Extended
                          format: int32
                          maximum: 65535
                          minimum: 1
                          type: integer
                        sectionName:
                          description: |-
                            SectionName is the name of a section within the target resource. In the
                            following resources, SectionName is interpreted as the following:

                            * Gateway: Listener name. When both Port (experimental) and SectionName
                            are specified, the name and port of the selected listener must match
                            both specified values.
                            * Service: Port name. When both Port (experimental) and SectionName
                            are specified, the name and port of the selected listener must match
                            both specified values.

                            Implementations MAY choose to support attaching Routes to other resources.
                            If that is the case, they MUST clearly document how SectionName is
                            interpreted.

                            When unspecified (empty string), this will reference the entire resource.
                            For the purpose of status, an attachment is considered successful if at
                            least one section in the parent resource accepts it. For example, Gateway
                            listeners can restrict which Routes can attach to them by Route kind,
                            namespace, or hostname. If 1 of 2 Gateway listeners accept attachment from
                            the referencing Route, the Route MUST be considered successfully
                            attached. If no Gateway listeners accept attachment from this Route, the
                            Route MUST be considered detached from the Gateway.

                            Support: Core
                          maxLength: 253
                          minLength: 1
                          pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                          type: string
                      required:
                      - name
                      type: object
                    conditions:
                      description: Conditions describes the status of the Policy with
                        respect to the given Ancestor.
                      items:
                        description: Condition contains details for one aspect of
                          the current state of this API Resource.
                        properties:
                          lastTransitionTime:
                            description: |-
                              lastTransitionTime is the last time the condition transitioned from one status to another.
                              This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                            format: date-time
                            type: string
                          message:
                            description: |-
                              message is a human readable message indicating details about the transition.
                              This may be an empty string.
                            maxLength: 32768
                            type: string
                          observedGeneration:
                            description: |-
                              observedGeneration represents the .metadata.generation that the condition was set based upon.
                              For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                              with respect to the current state of the instance.
                            format: int64
                            minimum: 0
                            type: integer
                          reason:
                            description: |-
                              reason contains a programmatic identifier indicating the reason for the condition's last transition.
                              Producers of specific condition types may define expected values and meanings for this field,
                              and whether the values are considered a guaranteed API.
                              The value should be a CamelCase string.
                              This field may not be empty.
                            maxLength: 1024
                            minLength: 1
                            pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                            type: string
                          status:
                            description: status of the condition, one of True, False,
                              Unknown.
                            enum:
                            - "True"
                            - "False"
                            - Unknown
                            type: string
                          type:
                            description: type of condition in CamelCase or in foo.example.com/CamelCase.
                            maxLength: 316
                            pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                            type: string
                        required:
                        - lastTransitionTime
                        - message
                        - reason
                        - status
                        - type
                        type: object
                      maxItems: 8
                      minItems: 1
                      type: array
                      x-kubernetes-list-map-keys:
                      - type
                      x-kubernetes-list-type: map
                    controllerName:
                      description: |-
                        ControllerName is a domain/path string that indicates the name of the
                        controller that wrote this status. This corresponds with the
                        controllerName field on GatewayClass.

                        Example: "example.net/gateway-controller".

                        The format of this field is DOMAIN "/" PATH, where DOMAIN and PATH are
                        valid Kubernetes names
                        (https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names).

                        Controllers MUST populate this field when writing status. Controllers should ensure that
                        entries to status populated with their ControllerName are cleaned up when they are no
                        longer necessary.
                      maxLength: 253
                      minLength: 1
                      pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*\/[A-Za-z0-9\/\-._~%!$&'()*+,;=:]+$
                      type: string
                  required:
                  - ancestorRef
                  - controllerName
                  type: object
                maxItems: 16
                type: array
            required:
            - ancestors
            type: object
        type: object
        x-kubernetes-validations:
        - message: Only one of spec.hashOn.(input|cookie|header|uriCapture|queryArg)
            can be set.
          rule: 'has(self.spec.hashOn) ? [has(self.spec.hashOn.input), has(self.spec.hashOn.cookie),
            has(self.spec.hashOn.header), has(self.spec.hashOn.uriCapture), has(self.spec.hashOn.queryArg)].filter(fieldSet,
            fieldSet == true).size() <= 1 : true'
        - message: When spec.hashOn.cookie is set, spec.hashOn.cookiePath is required.
          rule: 'has(self.spec.hashOn) && has(self.spec.hashOn.cookie) ? has(self.spec.hashOn.cookiePath)
            : true'
        - message: When spec.hashOn.cookiePath is set, spec.hashOn.cookie is required.
          rule: 'has(self.spec.hashOn) && has(self.spec.hashOn.cookiePath) ? has(self.spec.hashOn.cookie)
            : true'
        - message: spec.algorithm must be set to "consistent-hashing" when spec.hashOn
            is set.
          rule: 'has(self.spec.hashOn) ? has(self.spec.algorithm) && self.spec.algorithm
            == "consistent-hashing" : true'
        - message: Only one of spec.hashOnFallback.(input|header|uriCapture|queryArg)
            can be set.
          rule: 'has(self.spec.hashOnFallback) ? [has(self.spec.hashOnFallback.input),
            has(self.spec.hashOnFallback.header), has(self.spec.hashOnFallback.uriCapture),
            has(self.spec.hashOnFallback.queryArg)].filter(fieldSet, fieldSet == true).size()
            <= 1 : true'
        - message: spec.algorithm must be set to "consistent-hashing" when spec.hashOnFallback
            is set.
          rule: 'has(self.spec.hashOnFallback) ? has(self.spec.algorithm) && self.spec.algorithm
            == "consistent-hashing" : true'
        - message: spec.hashOnFallback.cookie must not be set.
          rule: 'has(self.spec.hashOnFallback) ? !has(self.spec.hashOnFallback.cookie)
            : true'
        - message: spec.hashOnFallback.cookiePath must not be set.
          rule: 'has(self.spec.hashOnFallback) ? !has(self.spec.hashOnFallback.cookiePath)
            : true'
        - message: spec.healthchecks.passive.healthy.interval must not be set.
          rule: 'has(self.spec.healthchecks) && has(self.spec.healthchecks.passive)
            && has(self.spec.healthchecks.passive.healthy) ? !has(self.spec.healthchecks.passive.healthy.interval)
            : true'
        - message: spec.healthchecks.passive.unhealthy.interval must not be set.
          rule: 'has(self.spec.healthchecks) && has(self.spec.healthchecks.passive)
            && has(self.spec.healthchecks.passive.unhealthy) ? !has(self.spec.healthchecks.passive.unhealthy.interval)
            : true'
        - message: spec.hashOnFallback must not be set when spec.hashOn.cookie is
            set.
          rule: 'has(self.spec.hashOn) && has(self.spec.hashOn.cookie) ? !has(self.spec.hashOnFallback)
            : true'
        - message: When spec.stickySessions is set, spec.hashOn must be unset or set
            to 'none' (no other hash_on fields allowed).
          rule: 'has(self.spec.stickySessions) ? !has(self.spec.hashOn) || (has(self.spec.hashOn)
            && has(self.spec.hashOn.input) && self.spec.hashOn.input == ''none'' &&
            !has(self.spec.hashOn.cookie) && !has(self.spec.hashOn.header) && !has(self.spec.hashOn.uriCapture)
            && !has(self.spec.hashOn.queryArg)) : true'
        - message: spec.hashOnFallback must not be set when spec.stickySessions is
            set.
          rule: 'has(self.spec.stickySessions) ? !has(self.spec.hashOnFallback) :
            true'
        - message: spec.stickySessions.cookie is required when spec.stickySessions
            is set.
          rule: 'has(self.spec.stickySessions) ? has(self.spec.stickySessions.cookie)
            : true'
    served: true
    storage: true
    subresources:
      status: {}
