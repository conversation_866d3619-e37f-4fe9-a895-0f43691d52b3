version: "2"
linters:
  default: none
  enable:
    - kubeapilinter
  settings:
    custom:
      kubeapilinter:
        type: module
        description: Kube API LInter lints Kube like APIs based on API conventions and best practices.
        settings:
          linters:
            disable:
              - "*"
            enable:
              - jsontags
              - duplicatemarkers
              # - maxlength
              - nofloats
              - nomaps
              - nophase
              # - optionalorrequired
              - requiredfields
              - statusoptional
              - statussubresource
          lintersConfig:
            conditions:
              isFirstField: Warn
              useProtobuf: Warn
              usePatchStrategy: Warn
            nomaps:
              policy: AllowStringToStringMaps
            # We allow underscores as that's what many Konnect related fields use.
            jsonTags:
              jsonTagRegex: "^[a-z][a-z0-9_]*(?:[A-Z][a-z0-9_]*)*$"

